use strsim::{normalized_le<PERSON><PERSON><PERSON>, jaro_winkler};
use std::collections::HashSet;
use serde_derive::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MatchType {
    Exact,    // 精确匹配
    Similar,  // 相似匹配
    Partial,  // 部分匹配
}

impl MatchType {
    pub fn as_str(&self) -> &'static str {
        match self {
            MatchType::Exact => "exact",
            MatchType::Similar => "similar",
            MatchType::Partial => "partial",
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SimilarityMatch {
    pub similarity: f64,
    pub match_type: MatchType,
}

#[derive(<PERSON>lone)]
pub struct SimilarityCalculator {
    exact_threshold: f64,      // 精确匹配阈值
    similar_threshold: f64,    // 相似匹配阈值
    partial_threshold: f64,    // 部分匹配阈值
}

impl SimilarityCalculator {
    pub fn new(exact_threshold: f64, similar_threshold: f64, partial_threshold: f64) -> Self {
        Self {
            exact_threshold,
            similar_threshold,
            partial_threshold,
        }
    }

    /// 计算两个句子的相似度
    pub fn calculate_similarity(&self, sentence1: &str, sentence2: &str) -> Option<SimilarityMatch> {
        // 预处理句子
        let s1 = self.preprocess_sentence(sentence1);
        let s2 = self.preprocess_sentence(sentence2);
        
        // 如果句子太短，跳过
        if s1.len() < 5 || s2.len() < 5 {
            return None;
        }
        
        // 1. 检查精确匹配
        if self.is_exact_match(&s1, &s2) {
            return Some(SimilarityMatch {
                similarity: 1.0,
                match_type: MatchType::Exact,
            });
        }
        
        // 2. 计算综合相似度
        let similarity = self.compute_comprehensive_similarity(&s1, &s2);
        
        // 3. 根据阈值确定匹配类型
        if similarity >= self.exact_threshold {
            Some(SimilarityMatch {
                similarity,
                match_type: MatchType::Exact,
            })
        } else if similarity >= self.similar_threshold {
            Some(SimilarityMatch {
                similarity,
                match_type: MatchType::Similar,
            })
        } else if similarity >= self.partial_threshold {
            Some(SimilarityMatch {
                similarity,
                match_type: MatchType::Partial,
            })
        } else {
            None
        }
    }

    /// 预处理句子：去除标点符号、统一大小写、去除多余空格
    fn preprocess_sentence(&self, sentence: &str) -> String {
        sentence
            .chars()
            .filter(|c| c.is_alphanumeric() || c.is_whitespace() || self.is_chinese_char(*c))
            .collect::<String>()
            .to_lowercase()
            .split_whitespace()
            .collect::<Vec<&str>>()
            .join(" ")
    }

    /// 判断是否为中文字符
    fn is_chinese_char(&self, c: char) -> bool {
        matches!(c, '\u{4e00}'..='\u{9fff}')
    }

    /// 检查是否为精确匹配
    fn is_exact_match(&self, s1: &str, s2: &str) -> bool {
        s1 == s2
    }

    /// 计算综合相似度
    fn compute_comprehensive_similarity(&self, s1: &str, s2: &str) -> f64 {
        // 1. 编辑距离相似度 (Levenshtein)
        let levenshtein_sim = normalized_levenshtein(s1, s2);
        
        // 2. Jaro-Winkler 相似度
        let jaro_winkler_sim = jaro_winkler(s1, s2);
        
        // 3. 词汇重叠相似度
        let word_overlap_sim = self.calculate_word_overlap_similarity(s1, s2);
        
        // 4. 字符n-gram相似度
        let ngram_sim = self.calculate_ngram_similarity(s1, s2, 2);
        
        // 5. 包含关系检查
        let containment_bonus = self.calculate_containment_bonus(s1, s2);
        
        // 加权平均
        let base_similarity = levenshtein_sim * 0.3 
            + jaro_winkler_sim * 0.25 
            + word_overlap_sim * 0.25 
            + ngram_sim * 0.2;
        
        // 添加包含关系奖励
        (base_similarity + containment_bonus).min(1.0)
    }

    /// 计算词汇重叠相似度
    fn calculate_word_overlap_similarity(&self, s1: &str, s2: &str) -> f64 {
        let words1: HashSet<&str> = s1.split_whitespace().collect();
        let words2: HashSet<&str> = s2.split_whitespace().collect();
        
        if words1.is_empty() && words2.is_empty() {
            return 1.0;
        }
        
        let intersection = words1.intersection(&words2).count();
        let union = words1.union(&words2).count();
        
        if union == 0 {
            0.0
        } else {
            intersection as f64 / union as f64
        }
    }

    /// 计算n-gram相似度
    fn calculate_ngram_similarity(&self, s1: &str, s2: &str, n: usize) -> f64 {
        let ngrams1 = self.generate_ngrams(s1, n);
        let ngrams2 = self.generate_ngrams(s2, n);
        
        if ngrams1.is_empty() && ngrams2.is_empty() {
            return 1.0;
        }
        
        let intersection = ngrams1.intersection(&ngrams2).count();
        let union = ngrams1.union(&ngrams2).count();
        
        if union == 0 {
            0.0
        } else {
            intersection as f64 / union as f64
        }
    }

    /// 生成n-gram
    fn generate_ngrams(&self, text: &str, n: usize) -> HashSet<String> {
        let chars: Vec<char> = text.chars().collect();
        let mut ngrams = HashSet::new();
        
        if chars.len() >= n {
            for i in 0..=chars.len() - n {
                let ngram: String = chars[i..i + n].iter().collect();
                ngrams.insert(ngram);
            }
        }
        
        ngrams
    }

    /// 计算包含关系奖励
    fn calculate_containment_bonus(&self, s1: &str, s2: &str) -> f64 {
        let longer = if s1.len() > s2.len() { s1 } else { s2 };
        let shorter = if s1.len() > s2.len() { s2 } else { s1 };
        
        if longer.contains(shorter) {
            0.1 // 给予10%的奖励
        } else {
            0.0
        }
    }

    /// 批量计算相似度
    pub fn calculate_similarities(&self, sentences1: &[String], sentences2: &[String]) -> Vec<(usize, usize, SimilarityMatch)> {
        let mut matches = Vec::new();
        
        for (i, s1) in sentences1.iter().enumerate() {
            for (j, s2) in sentences2.iter().enumerate() {
                if let Some(similarity_match) = self.calculate_similarity(s1, s2) {
                    matches.push((i, j, similarity_match));
                }
            }
        }
        
        // 按相似度降序排序
        matches.sort_by(|a, b| b.2.similarity.partial_cmp(&a.2.similarity).unwrap());
        
        matches
    }

    /// 设置新的阈值
    pub fn set_thresholds(&mut self, exact: f64, similar: f64, partial: f64) {
        self.exact_threshold = exact;
        self.similar_threshold = similar;
        self.partial_threshold = partial;
    }
}

impl Default for SimilarityCalculator {
    fn default() -> Self {
        Self::new(0.95, 0.7, 0.5)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_exact_match() {
        let calculator = SimilarityCalculator::default();
        let s1 = "这是一个测试句子。";
        let s2 = "这是一个测试句子。";
        
        let result = calculator.calculate_similarity(s1, s2).unwrap();
        assert_eq!(result.similarity, 1.0);
        assert!(matches!(result.match_type, MatchType::Exact));
    }

    #[test]
    fn test_similar_match() {
        let calculator = SimilarityCalculator::default();
        let s1 = "这是一个测试句子。";
        let s2 = "这是一个测试语句。";
        
        let result = calculator.calculate_similarity(s1, s2);
        assert!(result.is_some());
        let result = result.unwrap();
        assert!(result.similarity > 0.7);
    }

    #[test]
    fn test_no_match() {
        let calculator = SimilarityCalculator::default();
        let s1 = "这是一个测试句子。";
        let s2 = "完全不同的内容。";
        
        let result = calculator.calculate_similarity(s1, s2);
        // 可能没有匹配，或者相似度很低
        if let Some(result) = result {
            assert!(result.similarity < 0.7);
        }
    }
}
