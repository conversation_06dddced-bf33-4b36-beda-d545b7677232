use regex::Regex;
use std::collections::HashMap;
use serde_derive::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Sentence {
    pub content: String,
    pub book_id: String,
    pub book_name: String,
    pub page: i32,
    pub start_pos: usize,
    pub end_pos: usize,
}

#[derive(Clone)]
pub struct SentenceExtractor {
    min_length: usize,
    sentence_regex: Regex,
}

impl SentenceExtractor {
    pub fn new(min_length: usize) -> Self {
        // 中文句子分割正则表达式，匹配句号、问号、感叹号等
        let sentence_regex = Regex::new(r"[。！？；\.\!\?;]+").unwrap();
        
        Self {
            min_length,
            sentence_regex,
        }
    }

    /// 从书籍内容中提取句子
    pub fn extract_sentences_from_book(&self, book_id: &str, book_name: &str, content: &str) -> Vec<Sentence> {
        let mut sentences = Vec::new();
        
        // 按页分割内容（这里简化处理，实际应该根据具体的书籍格式来分割）
        let pages = self.split_into_pages(content);
        
        for (page_num, page_content) in pages.iter().enumerate() {
            let page_sentences = self.extract_sentences_from_page(
                book_id, 
                book_name, 
                page_content, 
                (page_num + 1) as i32
            );
            sentences.extend(page_sentences);
        }
        
        sentences
    }

    /// 从单页内容中提取句子
    fn extract_sentences_from_page(&self, book_id: &str, book_name: &str, content: &str, page: i32) -> Vec<Sentence> {
        let mut sentences = Vec::new();
        let mut current_pos = 0;
        
        // 使用正则表达式分割句子
        for mat in self.sentence_regex.find_iter(content) {
            let sentence_end = mat.end();
            let sentence_content = content[current_pos..sentence_end].trim();
            
            // 过滤掉太短的句子和只包含标点符号的句子
            if sentence_content.len() >= self.min_length && self.is_valid_sentence(sentence_content) {
                sentences.push(Sentence {
                    content: sentence_content.to_string(),
                    book_id: book_id.to_string(),
                    book_name: book_name.to_string(),
                    page,
                    start_pos: current_pos,
                    end_pos: sentence_end,
                });
            }
            
            current_pos = sentence_end;
        }
        
        // 处理最后一个句子（如果没有以标点符号结尾）
        if current_pos < content.len() {
            let sentence_content = content[current_pos..].trim();
            if sentence_content.len() >= self.min_length && self.is_valid_sentence(sentence_content) {
                sentences.push(Sentence {
                    content: sentence_content.to_string(),
                    book_id: book_id.to_string(),
                    book_name: book_name.to_string(),
                    page,
                    start_pos: current_pos,
                    end_pos: content.len(),
                });
            }
        }
        
        sentences
    }

    /// 简单的页面分割逻辑（实际应该根据具体格式来实现）
    fn split_into_pages(&self, content: &str) -> Vec<String> {
        // 这里简化处理，假设每1000个字符为一页
        const CHARS_PER_PAGE: usize = 1000;
        
        let mut pages = Vec::new();
        let chars: Vec<char> = content.chars().collect();
        
        for chunk in chars.chunks(CHARS_PER_PAGE) {
            let page_content: String = chunk.iter().collect();
            pages.push(page_content);
        }
        
        if pages.is_empty() {
            pages.push(content.to_string());
        }
        
        pages
    }

    /// 验证句子是否有效
    fn is_valid_sentence(&self, sentence: &str) -> bool {
        // 检查句子是否包含足够的汉字或字母
        let char_count = sentence.chars()
            .filter(|c| c.is_alphabetic() || self.is_chinese_char(*c))
            .count();
        
        // 至少包含5个有效字符
        char_count >= 5
    }

    /// 判断是否为中文字符
    fn is_chinese_char(&self, c: char) -> bool {
        matches!(c, '\u{4e00}'..='\u{9fff}')
    }

    /// 清理句子内容，移除多余的空白字符和特殊符号
    pub fn clean_sentence(&self, sentence: &str) -> String {
        // 移除多余的空白字符
        let cleaned = sentence.split_whitespace().collect::<Vec<&str>>().join(" ");
        
        // 移除一些不必要的符号
        let cleaned = cleaned.replace("　", " "); // 全角空格
        let cleaned = cleaned.replace("&nbsp;", " ");
        
        cleaned.trim().to_string()
    }

    /// 批量提取多本书的句子
    pub fn extract_sentences_from_books(&self, books: &HashMap<String, (String, String)>) -> HashMap<String, Vec<Sentence>> {
        let mut all_sentences = HashMap::new();
        
        for (book_id, (book_name, content)) in books {
            let sentences = self.extract_sentences_from_book(book_id, book_name, content);
            all_sentences.insert(book_id.clone(), sentences);
        }
        
        all_sentences
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sentence_extraction() {
        let extractor = SentenceExtractor::new(10);
        let content = "这是第一个句子。这是第二个句子！这是第三个句子？这是一个很短的句子。";
        
        let sentences = extractor.extract_sentences_from_page("book1", "测试书籍", content, 1);
        
        assert_eq!(sentences.len(), 4);
        assert_eq!(sentences[0].content, "这是第一个句子。");
        assert_eq!(sentences[1].content, "这是第二个句子！");
    }

    #[test]
    fn test_sentence_cleaning() {
        let extractor = SentenceExtractor::new(5);
        let dirty_sentence = "  这是一个　　有很多空格的句子。  ";
        let cleaned = extractor.clean_sentence(dirty_sentence);
        
        assert_eq!(cleaned, "这是一个 有很多空格的句子。");
    }
}
