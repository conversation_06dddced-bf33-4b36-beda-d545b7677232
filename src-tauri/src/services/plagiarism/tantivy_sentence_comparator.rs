use std::collections::HashMap;
use std::path::PathBuf;
use tantivy::{doc, Index, ReloadPolicy, schema::{Schema, Field, TextOptions, TextFieldIndexing, IndexRecordOption, STORED, INDEXED}, query::{QueryParser}, collector::TopDocs, directory::MmapDirectory, TantivyDocument};
use tantivy_jieba::JiebaTokenizer;
use serde::{Serialize, Deserialize};
use tantivy::schema::Value;
use crate::utils::path::get_retrieval_dir;
use super::sentence_extractor::Sentence;
use super::similarity_calculator::{SimilarityCalculator, SimilarityMatch};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SentenceCandidate {
    pub sentence: Sentence,
    pub score: f64,
    pub similarity_match: Option<SimilarityMatch>,
}

pub struct TantivySentenceComparator {
    index: Option<Index>,
    schema: Schema,
    fields: SentenceFields,
    similarity_calculator: SimilarityCalculator,
}

#[derive(Clone)]
struct SentenceFields {
    id: Field,
    book_id: Field,
    page: Field,
    content: Field,
    content_length: Field,
}

impl TantivySentenceComparator {
    pub fn new(similarity_calculator: SimilarityCalculator) -> Self {
        let (schema, fields) = Self::create_schema();
        
        Self {
            index: None,
            schema,
            fields,
            similarity_calculator,
        }
    }

    fn create_schema() -> (Schema, SentenceFields) {
        let mut schema_builder = Schema::builder();
        // 为 ID 类字段和书名定义通用的文本选项：存储并索引为单个词元
        let id_like_text_options = TextOptions::default()
            .set_stored() // 确保字段值被存储
            .set_indexing_options( // 配置索引选项
                                   TextFieldIndexing::default()
                                       .set_tokenizer("raw") // "raw" 分词器将整个字符串视为单个词元
                                       .set_index_option(IndexRecordOption::Basic) // 基本索引，记录词元出现即可
            );

        // 句子ID字段
        let id = schema_builder.add_text_field("id", id_like_text_options.clone());
        
        // 书籍ID字段
        let book_id = schema_builder.add_text_field("book_id", id_like_text_options.clone());
        
        // 页码字段
        let page = schema_builder.add_i64_field("page", STORED | INDEXED);
        
        // 句子内容字段 - 使用中文分词
        let text_field_indexing = TextFieldIndexing::default()
            .set_tokenizer("jieba")
            .set_index_option(IndexRecordOption::WithFreqsAndPositions);
        let text_options = TextOptions::default().set_indexing_options(text_field_indexing).set_stored();
        let content = schema_builder.add_text_field("content", text_options);
        
        // 句子长度字段 - 用于过滤
        let content_length = schema_builder.add_i64_field("content_length", STORED | INDEXED);

        let schema = schema_builder.build();
        let fields = SentenceFields {
            id,
            book_id,
            page,
            content,
            content_length,
        };

        (schema, fields)
    }

    /// 初始化或打开索引
    pub async fn initialize_index(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let index_path = self.get_index_path()?;
        
        // 确保目录存在
        if let Some(parent) = index_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let dir = MmapDirectory::open(index_path)?;
        let index = Index::open_or_create(dir, self.schema.clone())?;

        // 注册中文分词器
        let tokenizer = JiebaTokenizer {};
        index.tokenizers().register("jieba", tokenizer);

        self.index = Some(index);
        Ok(())
    }

    fn get_index_path(&self) -> Result<PathBuf, Box<dyn std::error::Error>> {
        let mut path = get_retrieval_dir()?;
        path.push("plagiarism_sentences");
        Ok(path)
    }

    /// 为句子集合建立索引
    pub async fn build_index(&mut self, all_sentences: &HashMap<String, Vec<Sentence>>) -> Result<(), Box<dyn std::error::Error>> {
        if self.index.is_none() {
            self.initialize_index().await?;
        }

        let index = self.index.as_ref().unwrap();
        let mut index_writer = index.writer(50_000_000)?; // 50MB buffer

        // 清空现有索引
        index_writer.delete_all_documents()?;

        let mut sentence_id = 0u64;
        
        // 为每个句子创建文档
        for (book_id, sentences) in all_sentences {
            for sentence in sentences {
                let doc = doc!(
                    self.fields.id => format!("{}_{}", book_id, sentence_id),
                    self.fields.book_id => book_id.clone(),
                    self.fields.page => sentence.page as i64,
                    self.fields.content => sentence.content.clone(),
                    self.fields.content_length => sentence.content.len() as i64,
                );
                
                index_writer.add_document(doc)?;
                sentence_id += 1;
            }
        }

        index_writer.commit()?;
        index_writer.wait_merging_threads()?;
        
        Ok(())
    }

    /// 查找与给定句子相似的候选句子
    pub async fn find_similar_sentences(
        &self,
        target_sentence: &Sentence,
        min_sentence_length: i32,
        max_candidates: usize,
        book_id_to_name: &HashMap<String, String>,
    ) -> Result<Vec<SentenceCandidate>, Box<dyn std::error::Error>> {
        if self.index.is_none() {
            return Err("Index not initialized".into());
        }

        let index = self.index.as_ref().unwrap();
        let reader = index
            .reader_builder()
            .reload_policy(ReloadPolicy::OnCommitWithDelay)
            .try_into()?;
        
        let searcher = reader.searcher();

        // 使用句子内容进行查询
        let query_parser = QueryParser::for_index(index, vec![self.fields.content]);
        // 将内容作为短语查询，以避免特殊字符解析问题
        let escaped_content = format!("\"{}\"", target_sentence.content.replace('"', "\\\"")); // 转义内容中的双引号
        let query = query_parser.parse_query(&escaped_content)?;

        // 搜索相似句子，获取更多候选以便后续精确计算
        let top_docs = searcher.search(&query, &TopDocs::with_limit(max_candidates * 3))?;

        let mut candidates = Vec::new();

        for (score, doc_address) in top_docs {
            let doc = searcher.doc::<TantivyDocument>(doc_address)?;
            // 提取文档字段
            let content = doc.get_first(self.fields.content)
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();
            
            let book_id = doc.get_first(self.fields.book_id)
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();

            // 从传入的映射中获取书籍名称
            let book_name = book_id_to_name.get(&book_id)
                .cloned()
                .unwrap_or_else(|| format!("书籍{}", book_id));
            
            let page = doc.get_first(self.fields.page)
                .and_then(|v| v.as_i64())
                .unwrap_or(0) as i32;

            // 过滤条件：
            // 1. 不是同一本书的句子
            // 2. 句子长度满足要求
            // 3. 不是完全相同的句子
            if book_id != target_sentence.book_id 
                && content.len() >= min_sentence_length as usize
                && content != target_sentence.content {
                
                let candidate_sentence = Sentence {
                    book_id,
                    book_name,
                    page,
                    content,
                    start_pos: 0,
                    end_pos: 0,
                };

                // 使用现有的相似度计算器进行精确计算
                let similarity_match = self.similarity_calculator.calculate_similarity(
                    &target_sentence.content,
                    &candidate_sentence.content,
                );

                candidates.push(SentenceCandidate {
                    sentence: candidate_sentence,
                    score: score as f64,
                    similarity_match,
                });
            }
        }

        // 按Tantivy得分排序，然后限制数量
        candidates.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap());
        candidates.truncate(max_candidates);

        Ok(candidates)
    }

    /// 批量比较句子，返回所有匹配的句子对
    pub async fn batch_compare_sentences(
        &mut self,
        all_sentences: &HashMap<String, Vec<Sentence>>,
        min_sentence_length: i32,
        max_candidates_per_sentence: usize,
    ) -> Result<Vec<(Sentence, SentenceCandidate)>, Box<dyn std::error::Error>> {
        // 首先建立索引
        self.build_index(all_sentences).await?;

        // 创建 book_id 到 book_name 的映射
        let mut book_id_to_name = HashMap::new();
        for sentences in all_sentences.values() {
            for sentence in sentences {
                book_id_to_name.insert(sentence.book_id.clone(), sentence.book_name.clone());
            }
        }

        let mut all_matches = Vec::new();

        // 为每个句子查找相似的候选句子
        for sentences in all_sentences.values() {
            for sentence in sentences {
                if sentence.content.len() < min_sentence_length as usize {
                    continue;
                }

                let candidates = self.find_similar_sentences(
                    sentence,
                    min_sentence_length,
                    max_candidates_per_sentence,
                    &book_id_to_name,
                ).await?;

                // 只保留有相似度匹配的候选
                for candidate in candidates {
                    if candidate.similarity_match.is_some() {
                        all_matches.push((sentence.clone(), candidate));
                    }
                }
            }
        }

        Ok(all_matches)
    }
}

impl Default for TantivySentenceComparator {
    fn default() -> Self {
        Self::new(SimilarityCalculator::default())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    #[tokio::test]
    async fn test_tantivy_sentence_comparator_creation() {
        let similarity_calculator = SimilarityCalculator::default();
        let comparator = TantivySentenceComparator::new(similarity_calculator);

        // 测试创建成功
        assert!(comparator.index.is_none());
    }

    #[tokio::test]
    async fn test_tantivy_index_initialization() {
        let similarity_calculator = SimilarityCalculator::default();
        let mut comparator = TantivySentenceComparator::new(similarity_calculator);

        // 测试索引初始化
        let result = comparator.initialize_index().await;
        if let Err(ref e) = result {
            println!("Index initialization failed: {}", e);
        }
        assert!(result.is_ok(), "Index initialization should succeed: {:?}", result);
        assert!(comparator.index.is_some());
    }

    #[tokio::test]
    async fn test_sentence_indexing_and_search() {
        let similarity_calculator = SimilarityCalculator::default();
        let mut comparator = TantivySentenceComparator::new(similarity_calculator);

        // 创建测试数据
        let mut all_sentences = HashMap::new();

        let sentences1 = vec![
            Sentence {
                content: "这是一个测试句子，用于验证Tantivy的功能。".to_string(),
                book_id: "book1".to_string(),
                book_name: "测试书籍1".to_string(),
                page: 1,
                start_pos: 0,
                end_pos: 20,
            },
        ];

        let sentences2 = vec![
            Sentence {
                content: "这是一个相似的测试句子，用于验证功能。".to_string(),
                book_id: "book2".to_string(),
                book_name: "测试书籍2".to_string(),
                page: 1,
                start_pos: 0,
                end_pos: 20,
            },
        ];

        all_sentences.insert("book1".to_string(), sentences1);
        all_sentences.insert("book2".to_string(), sentences2);

        // 创建book_id到book_name的映射
        let mut book_id_to_name = HashMap::new();
        book_id_to_name.insert("book1".to_string(), "测试书籍1".to_string());
        book_id_to_name.insert("book2".to_string(), "测试书籍2".to_string());

        // 测试索引构建
        let result = comparator.build_index(&all_sentences).await;
        assert!(result.is_ok());

        // 测试句子搜索
        let target_sentence = &all_sentences["book1"][0];
        let candidates = comparator.find_similar_sentences(
            target_sentence,
            10,
            10,
            &book_id_to_name
        ).await;

        assert!(candidates.is_ok());
        let candidates = candidates.unwrap();

        // 验证找到的候选句子不是来自同一本书
        for candidate in &candidates {
            assert_ne!(candidate.sentence.book_id, target_sentence.book_id);
        }
    }
}
